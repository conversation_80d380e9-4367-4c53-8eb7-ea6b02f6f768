/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable, InjectionToken, Injector } from '@angular/core';
import { DataServiceFlatNode, IDataServiceChildRoleOptions, IDataServiceEndPointOptions } from '@libs/platform/data-access';
import { ServiceRole } from '@libs/platform/data-access';
import { IDataServiceOptions } from '@libs/platform/data-access';
import { IIdentificationData, PlatformLazyInjectorService } from '@libs/platform/common';
import { BasicsEfbsheetsDataService } from './basics-efbsheets-data.service';
import { BasicsEfbsheetsComplete } from '../model/entities/basics-efbsheets-complete.class';
import { IBasicsEfbsheetsAverageWageComplete } from '../model/entities/basics-efbsheets-average-wage-complete.interface';
import { BASICS_EFBSHEETS_COMMON_SERVICE_TOKEN, childType, IBasicsEfbsheetsAverageWageEntity, IBasicsEfbsheetsEntity } from '@libs/basics/interfaces';
import { BasicsEfbsheetsCommonService } from './basics-efbsheets-common.service';

export const BASICS_EFBSHEETS_AVERAGE_WAGE_DATA_TOKEN = new InjectionToken<BasicsEfbsheetsAverageWageDataService>('basicsEfbsheetsAverageWageDataToken');

@Injectable({
	providedIn: 'root',
})
export class BasicsEfbsheetsAverageWageDataService extends DataServiceFlatNode<IBasicsEfbsheetsAverageWageEntity,IBasicsEfbsheetsAverageWageComplete, IBasicsEfbsheetsEntity, BasicsEfbsheetsComplete> {
	private parentService: BasicsEfbsheetsDataService;
	private readonly lazyInjector = inject(PlatformLazyInjectorService);
	public constructor(basicsEfbsheetsDataService: BasicsEfbsheetsDataService, private injector: Injector) {
		const options: IDataServiceOptions<IBasicsEfbsheetsAverageWageEntity> = {
			apiUrl: 'basics/efbsheets/averagewages',

			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'list',
				usePost: true,
				prepareParam: (ident: IIdentificationData) => {
					return { EstCrewMixFk: ident.pKey1 };
				},
			},
			updateInfo: <IDataServiceEndPointOptions>{
				endPoint: 'update'
			},
			roleInfo: <IDataServiceChildRoleOptions<IBasicsEfbsheetsAverageWageEntity, IBasicsEfbsheetsEntity, BasicsEfbsheetsComplete>>{
				role: ServiceRole.Node,
				itemName: 'EstAverageWage',
				parent: basicsEfbsheetsDataService
			},
		};

		super(options);
		this.parentService = basicsEfbsheetsDataService;
	}

	public canAverageWage() {
		const selectedCrewMix = this.parentService.getSelection();
		if (selectedCrewMix) {
			return true;
		} else {
			return false;
		}
	}

	public hasToLoadOnSelectionChange(crewmix: IBasicsEfbsheetsEntity) {
		if (crewmix) {
			//data.doNotLoadOnSelectionChange = false;
		}
	}

	/**
	 * Determines if the given entity is a child of the specified parent entity
	 * @param parentKey
	 * @param entity
	 * @returns
	 */
	public override isParentFn(parentKey: IBasicsEfbsheetsEntity, entity: IBasicsEfbsheetsAverageWageEntity): boolean {
		return entity.EstCrewMixFk === parentKey.Id;
	}

	/**
	 * This method registers the modofication and delete of Average Wages entity to provied update
	 * @param parentUpdate
	 * @param modified
	 * @param deleted
	 */
	public override registerNodeModificationsToParentUpdate(parentUpdate: BasicsEfbsheetsComplete, modified: IBasicsEfbsheetsAverageWageComplete[], deleted: IBasicsEfbsheetsAverageWageEntity[]): void {
		if (modified && modified.length > 0) {
			parentUpdate.EstAverageWageToSave = modified;
		}
		const selectedCreMix = this.parentService.getSelection()[0];
		if (deleted && deleted.length > 0) {
			parentUpdate.EstAverageWageToDelete = deleted;
			const basicsEfbsheetsCommonService = this.injector.get(BasicsEfbsheetsCommonService);
			basicsEfbsheetsCommonService.calculateCrewmixesAndChilds(selectedCreMix, childType.AverageWage);
			parentUpdate.EstCrewMix = selectedCreMix ?? null;
		}
	}

	/**
	 * This method created IBasicsEfbsheetsAverageWageComplete object
	 * @param modified
	 * @returns
	 */
	public override createUpdateEntity(modified: IBasicsEfbsheetsAverageWageEntity): IBasicsEfbsheetsAverageWageComplete {
		return  {
			Id : modified?.Id,
			EstAverageWage: modified
		} as unknown as IBasicsEfbsheetsAverageWageComplete;
	}

	/**
	 * This method always returns `true`, indicating that registration by method is enable
	 * @returns
	 */
	public override registerByMethod(): boolean {
		return true;
	}


	/**
	 * Gets the saved entity
	 * @param parentUpdate
	 * @returns
	 */
	public override getSavedEntitiesFromUpdate(parentUpdate: BasicsEfbsheetsComplete): IBasicsEfbsheetsAverageWageEntity[] {
		 if (parentUpdate && parentUpdate.EstAverageWageToSave) {
			return parentUpdate.EstAverageWageToSave ?? [];
		 }
		 return [];
	}

    /**
	 * @brief Retrieves the saved completes from the update process.
	 * @param parentUpdate The parent object (`IBasicsEfbsheetsComplete`) that holds the full update state.
	 * @returns An array of `IBasicsEfbsheetsAverageWageComplete` objects that have been saved.
	 */
	public override getSavedCompletesFromUpdate(parentUpdate: BasicsEfbsheetsComplete): IBasicsEfbsheetsAverageWageComplete[] {
		const entities = parentUpdate.EstAverageWageToSave ?? [];
		return entities.map(entity => this.createUpdateEntity(entity));
	}

	/**
	 * Handles the successful creation of an entity by casting and returning it.
	 * @param created
	 * @returns
	 */
	public override onCreateSucceeded(created: object): IBasicsEfbsheetsAverageWageEntity {
		 return created as unknown as IBasicsEfbsheetsAverageWageEntity;
	}

	/**
	 *  Handles the change event for a specific field in an entity.
	 * @param field
	 * @param entity
	 * @returns This method does not return anything
	 */
	public async fieldChangeForMaster(entity: IBasicsEfbsheetsAverageWageEntity, field: string, value: number): Promise<void> {
		const selectedCrewMix = this.parentService.getSelectedEntity();
		if (
			selectedCrewMix &&
			['Count', 'Supervisory', 'MarkupRate', 'MdcWageGroupFk'].includes(field)
		) {
			const basicsCommonService = await this.lazyInjector.inject(BASICS_EFBSHEETS_COMMON_SERVICE_TOKEN);
			const childTypes = [
				{ type: childType.AverageWage, extra: false },
				{ type: childType.CrewmixAF, extra: true },
				{ type: childType.CrewmixAFSN, extra: true },
				{ type: childType.NonwageCosts, extra: true }
			];
			for (const { type, extra } of childTypes) {
				basicsCommonService.calculateCrewmixesAndChilds(selectedCrewMix, type, extra);
			}
		}
	}
}
