/*
 * Copyright(c) RIB Software GmbH
 */

import { CompleteIdentification } from '@libs/platform/common';
import { IPrjEfbsheetsAverageWageEntity } from '@libs/project/interfaces';

export interface IProjectEfbsheetsAverageWageComplete extends CompleteIdentification<IPrjEfbsheetsAverageWageEntity> {
	Id: number | null;
	MainItemId: number | null;
    PrjCrewMixAverageWage: IPrjEfbsheetsAverageWageEntity | null;
}