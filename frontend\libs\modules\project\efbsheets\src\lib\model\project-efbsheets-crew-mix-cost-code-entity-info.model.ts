/*
 * Copyright(c) RIB Software GmbH
 */

import { EntityInfo } from '@libs/ui/business-base';
import { prefixAllTranslationKeys } from '@libs/platform/common';
import { createLookup, FieldType } from '@libs/ui/common';
import { EstimateShareCostCodesLookupService } from '@libs/estimate/shared';
import { ProjectEfbsheetsCrewMixCostCodeDataService } from '../services/project-efbsheets-crew-mix-cost-code-data.service';
import { ProjectCrewMixValidationService } from '../services/validation/project-crew-mix-validation.service';
import { ProjectCrewMixCostCodeValidationService } from '../services/project-crew-mix-to-cost-code-validation.service';
import { BaseValidationService } from '@libs/platform/data-access';
import { IPrjCrewMix2CostCodeEntity } from '@libs/project/interfaces';

export const PROJECT_EFBSHEETS_CREW_MIX_COST_CODE_ENTITY_INFO: EntityInfo = EntityInfo.create<IPrjCrewMix2CostCodeEntity>({

	grid: {
		title: { key: 'project.main.crewMixToCostCodes' },
	},
	form: {
		title: { key: 'project.main.crewMixToCostCodesDetails' },
		containerUuid: '4e0ba68eb6a94cd7bcada61f767c9fae',
	},
	dataService: (ctx) => ctx.injector.get(ProjectEfbsheetsCrewMixCostCodeDataService),
	dtoSchemeId: { moduleSubModule: 'Project.CrewMix', typeName: 'PrjCrewMix2CostCodeDto' },
	permissionUuid: '5fbf701267ea4e20b4723a7d46dbee24',
	validationService: (context) => context.injector.get(ProjectCrewMixCostCodeValidationService),
	layoutConfiguration: {
		 groups: [
                {
                    gid: 'basicData',
                    title: {
                        text: 'Basic Data',
                        key: 'cloud.common.entityProperties'
                    },
                    attributes: ['PrjProject2MdcCostCodeFk', 'Rate', 'RateHour']
                }
            ],
            labels: {
                ...prefixAllTranslationKeys('cloud.common.', {
                    PrjProject2MdcCostCodeFk: { key: 'entityCostCode' }
                }),
                ...prefixAllTranslationKeys('basics.costcodes.', {
                    Rate: { key: 'rate' },
                    RateHour: { key: 'rateHour' }
                }),

            },
            overloads: {
                PrjProject2MdcCostCodeFk: {
                    type: FieldType.Lookup,
                    lookupOptions: createLookup({
                        dataServiceToken: EstimateShareCostCodesLookupService,
                        showClearButton: true
                    }),
                    additionalFields: [
                        {
                            displayMember: 'DescriptionInfo.Translated',
                            label: 'cloud.common.entityDescription',
                            column: true,
                            row: true,
                        },
                        {
                            displayMember: 'Rate',
                            label: 'basics.costcodes.unitRate',
                            column: true,
                            row: true,
                        }
                    ],
                    visible: true,
                    readonly: false
                },
                Rate: {
                    type: FieldType.Money,
                    visible: true,
                    readonly: false
                },
                RateHour: {
                    type: FieldType.Money,
                    visible: true,
                    readonly: false
                }
            }
	}
});
