/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

import { IEntityBase } from '@libs/platform/common';
import { IPrjEfbsheetsCrewMixEntity } from './prj-efbsheets-crewmix-entity.interface';

export interface IPrjCrewMix2CostCodeEntityGenerated extends IEntityBase {

  /*
   * EstCrewMixEntity
   */
  EstCrewMixEntity?: IPrjEfbsheetsCrewMixEntity | null;

  /*
   * EstCrewMixFk
   */
  EstCrewMixFk?: number | null;

  /*
   * Id
   */
  Id: number;

  /*
   * MdcCostCodeFk
   */
  MdcCostCodeFk?: number | null;



  /*
   * prjproject2mdccostcodefk
   */
  PrjProject2MdcCostCodeFk?: number | null;


  /*
   * Rate
   */
  Rate?: number | null;

  /*
   * RateHour
   */
  RateHour?: number | null;

}
