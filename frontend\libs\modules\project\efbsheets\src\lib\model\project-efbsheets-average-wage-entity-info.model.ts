/*
 * Copyright(c) RIB Software GmbH
 */

import { EntityInfo } from '@libs/ui/business-base';
import { ProjectEfbsheetsAverageWageDataService } from '../services/project-efbsheets-average-wage-data.service';
import { prefixAllTranslationKeys } from '@libs/platform/common';
import { createLookup, FieldType } from '@libs/ui/common';
import { BasicsEfbSheetsWageGroupLookupService } from '@libs/basics/efbsheets';
import { ProjectCrewMixAverageWageValidationService } from '../services/validation/project-crew-mix-average-wage-validation.service';
import { IPrjEfbsheetsAverageWageEntity } from '@libs/project/interfaces';

export const PROJECT_EFBSHEETS_AVERAGE_WAGE_ENTITY_INFO: EntityInfo = EntityInfo.create<IPrjEfbsheetsAverageWageEntity>({
	grid: {
		title: { key: 'project.main.averageWage' },
	},
	form: {
		title: { key: 'project.main.averageWageDetails' },
		containerUuid: 'b9e35e5438c441c1ad3e056f7fcf29e8',
	},
	dataService: (ctx) => ctx.injector.get(ProjectEfbsheetsAverageWageDataService),
	dtoSchemeId: { moduleSubModule: 'Project.CrewMix', typeName: 'PrjCrewMixAverageWageDto' },
	permissionUuid: 'f90a007080a5434bba20abd90a6ce823',
	validationService: (context) => context.injector.get(ProjectCrewMixAverageWageValidationService),
    
    layoutConfiguration: {
		groups: [
			{
				gid: 'basicData',
				attributes: ['Count', 'Supervisory', 'MdcWageGroupFk', 'MarkupRate']
			}
		],
		overloads: {
			MdcWageGroupFk: {
				lookupOptions: createLookup({
					dataServiceToken: BasicsEfbSheetsWageGroupLookupService,
				}),
				type: FieldType.Lookup,
				visible: true
			}
		},
		labels: {
			...prefixAllTranslationKeys('basics.efbsheets.', {
				Count: { key: 'count' },
				Supervisory: { key: 'supervisory' },
				MdcWageGroupFk: { key: 'entityWageGroup' },
				MarkupRate: { key: 'markupRate' }
			})
		}
	},
});
