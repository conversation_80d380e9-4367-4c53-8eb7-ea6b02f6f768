/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import {
    BaseValidationService,
    IEntityRuntimeDataRegistry,
    IValidationFunctions,
    ValidationInfo,
} from '@libs/platform/data-access';
import { ProjectEfbsheetsCrewMixCostCodeDataService } from './project-efbsheets-crew-mix-cost-code-data.service';
import { IPrjCrewMix2CostCodeEntity } from '@libs/project/interfaces';


/**
 * @class ProjectCrewMixValidationService
 * @description Provides validation methods for Project CrewMix instances
 */
@Injectable({
    providedIn: 'root'
})
export class ProjectCrewMixCostCodeValidationService extends BaseValidationService<IPrjCrewMix2CostCodeEntity> {
    protected dataService = inject(ProjectEfbsheetsCrewMixCostCodeDataService);

    /**
     * Generates the validation functions for Project CrewMix Cost Code
     * @returns An object containing the validation functions.
     */
    protected generateValidationFunctions(): IValidationFunctions<IPrjCrewMix2CostCodeEntity> {
        return {
            PrjProject2MdcCostCodeFk: [this.validateIsRequired]
        };
    }


    /**
     * Gets the entity runtime data registry for validation operations
     * @returns The data service that implements IEntityRuntimeDataRegistry
     */
    protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<IPrjCrewMix2CostCodeEntity> {
        return this.dataService;
    }

    /**
     * Gets the entity service for validation operations
     * @returns The data service that implements entity service APIs
     */
    protected override getEntityService = () => this.dataService;


}
