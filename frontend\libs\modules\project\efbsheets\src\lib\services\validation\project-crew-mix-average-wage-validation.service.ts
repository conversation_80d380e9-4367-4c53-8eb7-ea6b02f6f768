/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import {
    BaseValidationService,
    IEntityRuntimeDataRegistry,
    IValidationFunctions,
    ValidationInfo,
    ValidationResult
} from '@libs/platform/data-access';
import { ProjectEfbsheetsAverageWageDataService } from '../project-efbsheets-average-wage-data.service';
import { IBasicsEfbsheetsAverageWageEntity } from '@libs/basics/interfaces';
import { firstValueFrom } from 'rxjs';
import { BasicsEfbSheetsWageGroupLookupService } from '@libs/basics/efbsheets';
import { IPrjEfbsheetsAverageWageEntity } from '@libs/project/interfaces';


/**
 * @class ProjectCrewMixAverageWageValidationService
 * @description Provides validation methods for project crewmix average wage
 */
@Injectable({
    providedIn: 'root'
})
export class ProjectCrewMixAverageWageValidationService extends BaseValidationService<IPrjEfbsheetsAverageWageEntity> {
    protected dataService = inject(ProjectEfbsheetsAverageWageDataService);
    private basicsEfbSheetsWageGroupLookupService = inject(BasicsEfbSheetsWageGroupLookupService);

    /**
     * Generates the validation functions for Project CrewMix Average Wage
     * @returns An object containing the validation functions.
     */
    protected generateValidationFunctions(): IValidationFunctions<IPrjEfbsheetsAverageWageEntity> {
        return {
            MdcWageGroupFk: [this.validateMdcWageGroupFk],
            Count: [this.validateCountAsync],
            Supervisory: [this.validateSupervisoryAsync],
            MarkupRate: [this.validateMarkupRateAsync]
        };
    }

    /**
     * Gets the entity runtime data registry for validation operations
     * @returns The data service that implements IEntityRuntimeDataRegistry
     */
    protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<IPrjEfbsheetsAverageWageEntity> {
        return this.dataService;
    }

    /**
     * Validates the MdcWageGroupFk field and updates the MarkupRate based on the selected wage group
     * @param info Validation information for the Average Wage Entity
     * @returns ValidationResult indicating the validation result
     */
    protected async validateMdcWageGroupFk(info: ValidationInfo<IPrjEfbsheetsAverageWageEntity>): Promise<ValidationResult> {
        if (info.value) {
            try {
                const response = await firstValueFrom(this.basicsEfbSheetsWageGroupLookupService.getList());
                
                const customizeMarkupRate = response.find(item => item.Id === info.value);
                
                if (customizeMarkupRate) {
                    info.entity.MarkupRate = customizeMarkupRate.MarkupRate;
                }
            } catch (error) {
                console.error('Error during validation:', error);
            }
        }
        return new ValidationResult();
    }

    /**
     * Validates the MarkupRate field and triggers field change calculations
     * @param info Validation information for the Average Wage Entity
     * @returns ValidationResult indicating the validation result
     */
    private async validateMarkupRateAsync(info: ValidationInfo<IPrjEfbsheetsAverageWageEntity>): Promise<ValidationResult> {       
        if (this.dataService.fieldChangeForMaster) {
            this.dataService.fieldChangeForMaster(info.entity, 'MarkupRate', Number(info.value));
        }
        return new ValidationResult();
    }

    /**
     * Validates the Supervisory field and triggers field change calculations
     * @param info Validation information for the Average Wage Entity
     * @returns ValidationResult indicating the validation result
     */
    private async validateSupervisoryAsync(info: ValidationInfo<IPrjEfbsheetsAverageWageEntity>): Promise<ValidationResult> {
        if (this.dataService.fieldChangeForMaster) {
            this.dataService.fieldChangeForMaster(info.entity, 'Supervisory', Number(info.value));
        }
        return new ValidationResult();
    }

    /**
     * Validates the Count field and triggers field change calculations
     * @param info Validation information for the Average Wage Entity
     * @returns ValidationResult indicating the validation result
     */
    private async validateCountAsync(info: ValidationInfo<IPrjEfbsheetsAverageWageEntity>): Promise<ValidationResult> {
        if (this.dataService.fieldChangeForMaster) {
            this.dataService.fieldChangeForMaster(info.entity, 'Count', Number(info.value));
        }
        return new ValidationResult();
    }
}
