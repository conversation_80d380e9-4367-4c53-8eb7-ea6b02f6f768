/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable, InjectionToken} from '@angular/core';

import { ServiceRole, IDataServiceOptions, IDataServiceChildRoleOptions, IDataServiceEndPointOptions, DataServiceFlatNode } from '@libs/platform/data-access';
import { BASICS_EFBSHEETS_COMMON_SERVICE_TOKEN, childType } from '@libs/basics/interfaces';
import { ProjectEfbsheetsDataService } from './project-efbsheets-data.service';
import { PlatformLazyInjectorService } from '@libs/platform/common';
import { IPrjEfbsheetsAverageWageEntity, IPrjEfbsheetsCrewMixEntity } from '@libs/project/interfaces';
import { IPrjCrewMixComplete } from '../model/prj-crew-mix-complete.interface';
import { IProjectEfbsheetsAverageWageComplete } from '../model/project-efbsheest-average-wage-complete.interface';

export const PROJECT_EFBSHEETS_AVERAGE_WAGE_DATA_TOKEN = new InjectionToken<ProjectEfbsheetsAverageWageDataService>('projectEfbsheetsProjectAverageWageDataToken');
@Injectable({
	providedIn: 'root'
})

export class ProjectEfbsheetsAverageWageDataService extends DataServiceFlatNode<IPrjEfbsheetsAverageWageEntity, IProjectEfbsheetsAverageWageComplete, IPrjEfbsheetsCrewMixEntity, IPrjCrewMixComplete> {
	private readonly projectEfbsheetsProjectDataService = inject(ProjectEfbsheetsDataService);
	private readonly lazyInjector = inject(PlatformLazyInjectorService);
	public constructor(parentService: ProjectEfbsheetsDataService) {
		const options: IDataServiceOptions<IPrjEfbsheetsAverageWageEntity> = {
			apiUrl: 'project/crewmix/averagewages',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'list',
				usePost: true,
				prepareParam: (ident) => {
					const selectedProjectItem = this.projectEfbsheetsProjectDataService.getSelection()[0];
					return { estCrewMixFk: selectedProjectItem?.Id ?? 0 };
				}
			},
			updateInfo: <IDataServiceEndPointOptions>{
				endPoint: 'update',
				usePost: true
			},
			createInfo: <IDataServiceEndPointOptions>{
				endPoint: 'create',
				prepareParam: () => {
					const selection = this.projectEfbsheetsProjectDataService.getSelectedEntity();
					return {
						estCrewMixFk: selection?.Id ?? 0
					};
				}
			},
			roleInfo: <IDataServiceChildRoleOptions<IPrjEfbsheetsAverageWageEntity, IPrjEfbsheetsCrewMixEntity, IPrjCrewMixComplete>>{
				role: ServiceRole.Node,
				itemName: 'PrjCrewMixAverageWage',
				parent: parentService
			}
		};
		super(options);
	}

	/**
	 * @brief Indicates whether the current instance should be registered by method.
	 *
	 * This method always returns true, indicating that registration is permitted.
	 *
	 * @return `true` if registration by method is allowed; otherwise, `false`.
	 */
	public override registerByMethod(): boolean {
		return true;
	}

	/**
	 * @brief Provides the payload for loading data based on the selected parent.
	 *
	 * This method constructs an object containing the foreign key of the selected parent crew mix.
	 * It throws an error if no parent is selected.
	 *
	 * @return An object containing the `estCrewMixFk` property.
	 * @throws Error if no parent is selected.
	 */
	protected override provideLoadPayload(): object {
		const parent = this.getSelectedParent();

		if (parent) {
			return {
				estCrewMixFk: parent.Id
			};
		} else {
			throw new Error('There should be a selected parent');
		}
	}



	/**
	 * @brief Provides the payload for creating a new entity.
	 *
	 * This method generates the payload for creating a new entity by retrieving the selected parent entity
	 * and extracting its `Id` to set the `EstCrewMixFk` in the returned payload.
	 *
	 * @return An object containing the `EstCrewMixFk` property for creating a new entity.
	 */
	protected override provideCreatePayload(): object {
		const parentSelection = this.getSelectedParent();
		if (parentSelection) {
			return {
				EstCrewMixFk: parentSelection.Id
			};
		}

		return {};
	}




	/**
	 * @brief Creates a complete update entity from a modified average wage entity.
	 * @param modified The modified average wage entity or null.
	 * @returns A complete object containing the entity and its MainItemId.
	 */
	public override createUpdateEntity(modified: IPrjEfbsheetsAverageWageEntity | null): IProjectEfbsheetsAverageWageComplete {
		return {
			Id: modified?.Id ?? null,
			MainItemId: modified?.Id ?? null,
			PrjCrewMixAverageWage: modified ?? null
		} as IProjectEfbsheetsAverageWageComplete;
	}

	/**
	 * @brief Registers modifications and deletions of child nodes to the parent entity update.
	 *
	 * This method updates the parent entity (`PrjCrewMixComplete`) with the modified
	 * and deleted child nodes. It also recalculates crew mixes and child entities
	 * for the selected crew mix when deletions occur.
	 */
	public override registerNodeModificationsToParentUpdate(parentUpdate: IPrjCrewMixComplete, modified: IProjectEfbsheetsAverageWageComplete[], deleted: IPrjEfbsheetsAverageWageEntity[]): void {
		if (modified && modified.length > 0) {
			parentUpdate.PrjCrewMixAverageWageToSave = modified;
		}
		const selectedCreMix = this.projectEfbsheetsProjectDataService.getSelection()[0];
		if (deleted && deleted.length > 0) {
			parentUpdate.PrjCrewMixAverageWageToDelete = deleted;
			const basicsCommonToken = this.lazyInjector.inject(BASICS_EFBSHEETS_COMMON_SERVICE_TOKEN);
			basicsCommonToken.then(service => {
				service.calculateCrewmixesAndChilds(selectedCreMix, childType.AverageWage);
			});
			parentUpdate.PrjCrewMix = selectedCreMix ? [selectedCreMix] : null;
		}
	}


	/**
	 * @brief Retrieves the list of saved IPrjEfbsheetsAverageWageEntity entities from the parent update.
	 * @param parentUpdate The complete parent update object containing crew mix average wage data.
	 * @returns An array of IPrjEfbsheetsAverageWageEntity objects to be saved.
	 */
	public override getSavedEntitiesFromUpdate(parentUpdate: IPrjCrewMixComplete): IPrjEfbsheetsAverageWageEntity[] {
		return parentUpdate?.PrjCrewMixAverageWageToSave
			?.map(item => item.PrjCrewMixAverageWage)
			.filter((entity): entity is IPrjEfbsheetsAverageWageEntity => entity !== null) ?? [];
	}
	

	/**
	 * @brief Determines if the given entity is a child of the specified parent.
	 *
	 * This method compares the `EstCrewMixFk` property of the child entity
	 * (`IBasicsEfbsheetsAverageWageEntity`) with the `Id` of the parent entity
	 * (`IBasicsEfbsheetsEntity`) to determine a parent-child relationship.
	 * @return True if the `EstCrewMixFk` of the child entity matches the `Id` of the parent entity; false otherwise.
	 */
	public override isParentFn(parentKey: IPrjEfbsheetsCrewMixEntity, entity: IPrjEfbsheetsAverageWageEntity): boolean {
		return entity.EstCrewMixFk === parentKey.Id;
	}

	/**
	 * Handles the change event for a specific field in an entity.
	 * @param entity The entity being modified
	 * @param field The field name that changed
	 * @param value The new value for the field
	 * @returns This method does not return anything
	 */
	public async fieldChangeForMaster(_entity: IPrjEfbsheetsAverageWageEntity, field: string, _value: number): Promise<void> {
		const selectedCrewMix = this.projectEfbsheetsProjectDataService.getSelectedEntity();
		if (
			selectedCrewMix &&
			['Count', 'Supervisory', 'MarkupRate', 'MdcWageGroupFk'].includes(field)
		) {
			const basicsCommonToken = await this.lazyInjector.inject(BASICS_EFBSHEETS_COMMON_SERVICE_TOKEN);
			const childTypes = [
				{ type: childType.AverageWage, extra: false },
				{ type: childType.CrewmixAF, extra: true },
				{ type: childType.CrewmixAFSN, extra: true },
				{ type: childType.NonwageCosts, extra: true }
			];
			for (const { type, extra } of childTypes) {
				basicsCommonToken.calculateCrewmixesAndChilds(selectedCrewMix, type, extra);
			}
			this.projectEfbsheetsProjectDataService.setModified(selectedCrewMix);
		}
	}
}
