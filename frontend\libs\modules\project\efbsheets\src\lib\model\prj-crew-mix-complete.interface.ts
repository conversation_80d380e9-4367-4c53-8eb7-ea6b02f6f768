/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

import { CompleteIdentification } from '@libs/platform/common';
import { IPrjCrewMix2CostCodeEntity, IPrjEfbsheetsAverageWageEntity, IPrjEfbsheetsCrewMixEntity, IPrjEfbsheetsMixAfEntity, IPrjEfbsheetsMixAfsnEntity, IPrjEfbsheetsNonwageCostsEntity } from '@libs/project/interfaces';
import { ProjectEfbsheetNonWageComplete } from './project-efbsheets-crew-mix-nonwage-complet.class';
import { IProjectEfbsheetsCrewMixCostCodeComplete } from './project-efbsheets-crew-mix-cost-code-complete.interface';
import { IProjectEfbsheetsAverageWageComplete } from './project-efbsheest-average-wage-complete.interface';

export interface IPrjCrewMixComplete extends CompleteIdentification<IPrjEfbsheetsCrewMixEntity> {
      Id :number;
      MainItemId: number | null;
      PrjCrewMix: IPrjEfbsheetsCrewMixEntity[] | null;
      PrjCrewMixAverageWageToSave: IProjectEfbsheetsAverageWageComplete[] | null;
      PrjCrewMixAverageWageToDelete: IPrjEfbsheetsAverageWageEntity[] | null;
      PrjCrewMixAfToSave: IPrjEfbsheetsMixAfEntity[] | null;
      PrjCrewMixAfToDelete: IPrjEfbsheetsMixAfEntity[] | null;
      PrjCrewMixNonwageCostsToSave: ProjectEfbsheetNonWageComplete[] | null;
      PrjCrewMixNonwageCostsToDelete: IPrjEfbsheetsNonwageCostsEntity[] | null;
      PrjCrewMixAfsnToSave: IPrjEfbsheetsMixAfsnEntity[] | null;
      PrjCrewMixAfsnToDelete: IPrjEfbsheetsMixAfsnEntity[] | null;
      PrjCrewMix2CostCodeToSave: IProjectEfbsheetsCrewMixCostCodeComplete[] | null;
      PrjCrewMix2CostCodeToDelete: IPrjCrewMix2CostCodeEntity[] | null;

}
