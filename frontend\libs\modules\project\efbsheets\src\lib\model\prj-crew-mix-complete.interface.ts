/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

import { CompleteIdentification } from '@libs/platform/common';
import { IPrjCrewMix2CostCodeEntity, IPrjEfbsheetsAverageWageEntity, IPrjEfbsheetsCrewMixEntity, IPrjEfbsheetsMixAfEntity, IPrjEfbsheetsMixAfsnEntity, IPrjEfbsheetsNonwageCostsEntity } from '@libs/project/interfaces';
import { ProjectEfbsheetNonWageComplete } from './project-efbsheets-crew-mix-nonwage-complet.class';
import { IProjectEfbsheetsCrewMixCostCodeComplete } from './project-efbsheets-crew-mix-cost-code-complete.interface';
import { IProjectEfbsheetsAverageWageComplete } from './project-efbsheest-average-wage-complete.interface';
import { IProjectEfbsheetsCrewMixAfComplete } from './project-efbsheets-crew-mix-af-complete.interface';
import { IProjectEfbsheetsCrewMixAfsnComplete } from './project-efbsheets-crew-mix-afsn-complete.interface';

export interface IPrjCrewMixComplete extends CompleteIdentification<IPrjEfbsheetsCrewMixEntity> {
      Id :number;
      MainItemId: number | null;
      PrjCrewMix: IPrjEfbsheetsCrewMixEntity[] | null;
      PrjCrewMixAverageWageToSave: IProjectEfbsheetsAverageWageComplete[] | null;
      PrjCrewMixAverageWageToDelete: IPrjEfbsheetsAverageWageEntity[] | null;
      PrjCrewMixAfToSave: IProjectEfbsheetsCrewMixAfComplete[] | null;
      PrjCrewMixAfToDelete: IPrjEfbsheetsMixAfEntity[] | null;
      PrjCrewMixNonwageCostsToSave: ProjectEfbsheetNonWageComplete[] | null;
      PrjCrewMixNonwageCostsToDelete: IPrjEfbsheetsNonwageCostsEntity[] | null;
      PrjCrewMixAfsnToSave: IProjectEfbsheetsCrewMixAfsnComplete[] | null;
      PrjCrewMixAfsnToDelete: IPrjEfbsheetsMixAfsnEntity[] | null;
      PrjCrewMix2CostCodeToSave: IProjectEfbsheetsCrewMixCostCodeComplete[] | null;
      PrjCrewMix2CostCodeToDelete: IPrjCrewMix2CostCodeEntity[] | null;

}
